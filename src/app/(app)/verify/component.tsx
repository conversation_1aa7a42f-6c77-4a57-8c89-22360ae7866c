'use client'

import { zod<PERSON>esolver } from '@hookform/resolvers/zod'
import { useMutation } from '@tanstack/react-query'
import { ShieldCheck } from 'lucide-react'
import { useRouter, useSearchParams } from 'next/navigation'
import { email } from 'node_modules/payload/dist/fields/validations'
import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { MoonLoader } from 'react-spinners'
import { z } from 'zod'
import { Alert, AlertDescription, AlertTitle } from '@/app/(app)/_component/Alert'
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSeparator,
  InputOTPSlot,
} from '@/app/(app)/_component/Otp'
import { cn } from '@/lib/utils'
import { IconSize } from '@/utilities/local/css'
import { Button } from '../_component/Button'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '../_component/Form'
import { Input } from '../_component/Input'
import Text from '../_component/Text'
import Title, { HeaderLevel } from '../_component/Title'
import FlexContainer, { AlignItems, FlexDirection, JustifyContent } from '../_cssComp/FlexContainer'
import GridContainer, { GridItem, RowSpan, ColSpan } from '../_cssComp/GridContainer'
import useOtpRateLimit from '../_hooks/OtpRateLimit'
import { verifyCode, resendCode } from '../_localApi/users'
import { ApiResponse } from '../_localApi/util'
import { useAuthState } from '../_state/authState'
import { VerifyCodeResponse } from '../api/user/(register)/verifyCode/route'

const formSchema = z.object({
  email: z.string().email({ message: 'Invalid email address' }),
  code: z
    .string()
    .min(6, { message: 'Verification code must be 6 digits' })
    .max(6, { message: 'Verification code must be 6 digits' })
    .regex(/^\d{6}$/, { message: 'Verification code must be 6 digits' }),
})

export function VerifyEmailForm() {
  const searchParams = useSearchParams()
  const emailFromQuery = searchParams.get('email') || ''
  const router = useRouter()
  const [showAlert, setShowAlert] = useState(false)
  const [bLoading, setBLoading] = useState(false)
  const [alertMessage, setAlertMessage] = useState({ title: '', message: '' })
  const [resendLoading, setResendLoading] = useState(false)
  const setUser = useAuthState((state) => state.setUser)

  const otpRateLimit = useOtpRateLimit(emailFromQuery)

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: emailFromQuery,
      code: '',
    },
  })

  const verifyMutation = useMutation({
    mutationFn: (data: { email: string; code: string }) => verifyCode(data.email, data.code),
    onSuccess: (data: ApiResponse<VerifyCodeResponse>) => {
      console.log('data', data)
      setShowAlert(true)
      if (data.data.verified) {
        setAlertMessage({
          title: 'Success!',
          message: 'Your email has been verified.',
        })
        // login user
        setUser(data.data.user)
        // Redirect to login or home after a short delay
        setTimeout(() => router.push('/'), 1500)
      } else {
        setAlertMessage({
          title: 'Verification failed',
          message: data.message.message || 'Invalid code or email.',
        })
      }
    },
    onError: (error: any) => {
      setShowAlert(true)
      setAlertMessage({
        title: 'Error',
        message: error.message || 'Something went wrong.',
      })
    },
    onMutate: () => {
      setBLoading(true)
    },
    onSettled: () => {
      setBLoading(false)
    },
  })

  function onSubmit(values: z.infer<typeof formSchema>) {
    verifyMutation.mutate({
      email: values.email,
      code: values.code,
    })
  }

  // Handler for resending the code
  async function handleResendCode() {
    if (resendLoading) return
    setResendLoading(true)
    setShowAlert(false)
    try {
      const email = form.getValues('email')
      if (!email) {
        setAlertMessage({
          title: 'Error',
          message: 'Please enter your email to resend the code.',
        })
        setShowAlert(true)
        setResendLoading(false)
        return
      }
      const response = await resendCode(email)
      setAlertMessage({
        title: response.success ? 'Code Sent' : 'Error',
        message: response.success
          ? 'A new verification code has been sent to your email.'
          : response.message?.message || 'Failed to resend code.',
      })
      otpRateLimit.recordResendAttempt()
      setShowAlert(true)
    } catch (error: any) {
      setAlertMessage({
        title: 'Error',
        message: error.message || 'Failed to resend code.',
      })
      setShowAlert(true)
    }
    setResendLoading(false)
  }

  return (
    <FlexContainer
      className="bg-main border-2 border-black h-screen gap-2"
      direction={FlexDirection.COL}
      align={AlignItems.CENTER}
      justify={JustifyContent.CENTER}
    >
      <FlexContainer
        className="border-2 border-black p-4 gap-4 rounded-sm shadow-[3px_3px_0px_rgba(0,0,0,1)] bg-white w-[99%] sm:w-[95%] md:w-[70%] lg:w-[60%] xl:w-[50%] 2xl:w-[40%]"
        direction={FlexDirection.COL}
        align={AlignItems.CENTER}
      >
        <FlexContainer
          direction={FlexDirection.COL}
          className="gap-2 w-full"
          align={AlignItems.CENTER}
          justify={JustifyContent.CENTER}
        >
          <FlexContainer direction={FlexDirection.ROW} align={AlignItems.CENTER} className="gap-2">
            <ShieldCheck /> <Title level={HeaderLevel.H3}> Verify your email</Title>
          </FlexContainer>
          <Text
            variant="description"
            className="font-light"
          >{`We've sent an OTP to your email at ${emailFromQuery}.`}</Text>
        </FlexContainer>
        <Form {...form}>
          <FormField
            control={form.control}
            name="code"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <InputOTP maxLength={6} {...field}>
                    <InputOTPGroup>
                      <InputOTPSlot index={0} />
                      <InputOTPSlot index={1} />
                      <InputOTPSlot index={2} />
                      <InputOTPSlot index={3} />
                      <InputOTPSlot index={4} />
                      <InputOTPSlot index={5} />
                    </InputOTPGroup>
                  </InputOTP>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </Form>
        <Text>
          {`Didn't receive the code? `}
          {otpRateLimit.canResend && otpRateLimit.isHydrated ? (
            <span
              role="button"
              onClick={handleResendCode}
              className={cn(
                `text-accent2 hover:text-accent2-lighter underline cursor-pointer`,
                `${resendLoading ? 'cursor-not-allowed text-gray-500 hover:text-gray-500' : ''}`,
              )}
            >
              {resendLoading ? 'Resending...' : 'Resend OTP'}
            </span>
          ) : (
            <span className="text-gray-500">Resend in {otpRateLimit.waitTime} seconds</span>
          )}
        </Text>
        <Button variant="emphasis" className="w-full" onClick={form.handleSubmit(onSubmit)}>
          {bLoading ? (
            <MoonLoader size={IconSize.Small} />
          ) : (
            <>
              <ShieldCheck />
              Verify
            </>
          )}
        </Button>
      </FlexContainer>
    </FlexContainer>
  )
}

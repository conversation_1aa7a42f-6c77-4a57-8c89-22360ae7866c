import { useState, useEffect } from 'react'

interface RateLimitState {
  canResend: boolean
  waitTime: number
  lastSentAt: number | null
  isHydrated: boolean // Add this flag
}

export const useOtpRateLimit = (email: string) => {
  const [rateLimitState, setRateLimitState] = useState<RateLimitState>({
    canResend: true,
    waitTime: 0,
    lastSentAt: null,
    isHydrated: false, // Start as false
  })

  const COOLDOWN_SECONDS = 60
  const STORAGE_KEY = `otp_ratelimit_${email}`

  useEffect(() => {
    // This only runs on client-side
    const stored = localStorage.getItem(STORAGE_KEY)
    let newState = { isHydrated: true, canResend: true, waitTime: 0, lastSentAt: null }

    if (stored) {
      const data = JSON.parse(stored)
      const now = Date.now()
      const timeSinceLastSend = Math.floor((now - data.lastSentAt) / 1000)

      if (timeSinceLastSend < COOLDOWN_SECONDS) {
        newState = {
          isHydrated: true,
          canResend: false,
          waitTime: COOLDOWN_SECONDS - timeSinceLastSend,
          lastSentAt: data.lastSentAt,
        }
      }
    }

    setRateLimitState(newState)
  }, [email, STORAGE_KEY])

  useEffect(() => {
    // Countdown timer - only run if hydrated
    if (rateLimitState.isHydrated && !rateLimitState.canResend && rateLimitState.waitTime > 0) {
      const timer = setInterval(() => {
        setRateLimitState((prev) => {
          if (prev.waitTime <= 1) {
            return { ...prev, canResend: true, waitTime: 0 }
          }
          return { ...prev, waitTime: prev.waitTime - 1 }
        })
      }, 1000)

      return () => clearInterval(timer)
    }
  }, [rateLimitState.isHydrated, rateLimitState.canResend, rateLimitState.waitTime])

  const recordResendAttempt = () => {
    const now = Date.now()
    const newState = {
      isHydrated: true,
      canResend: false,
      waitTime: COOLDOWN_SECONDS,
      lastSentAt: now,
    }

    setRateLimitState(newState)
    localStorage.setItem(STORAGE_KEY, JSON.stringify({ lastSentAt: now }))
  }

  const resetRateLimit = () => {
    localStorage.removeItem(STORAGE_KEY)
    setRateLimitState((prev) => ({
      ...prev,
      canResend: true,
      waitTime: 0,
      lastSentAt: null,
    }))
  }

  return {
    canResend: rateLimitState.canResend,
    waitTime: rateLimitState.waitTime,
    isHydrated: rateLimitState.isHydrated, // Expose this
    recordResendAttempt,
    resetRateLimit,
  }
}

export default useOtpRateLimit

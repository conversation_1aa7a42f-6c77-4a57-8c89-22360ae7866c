import { Contrast, Download, ImageUpscale, Layers2 } from 'lucide-react'
import { useState } from 'react'
import { Badge } from '@/app/(app)/_component/Badge'
import { Button } from '@/app/(app)/_component/Button'
import { Checkbox } from '@/app/(app)/_component/Checkbox'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/app/(app)/_component/Dialog'
import Divider from '@/app/(app)/_component/Divider'
import ImageWithSkeleton from '@/app/(app)/_component/ImageWithSkeleton'
import { Label } from '@/app/(app)/_component/Label'
import Text from '@/app/(app)/_component/Text'
import Title, { HeaderLevel } from '@/app/(app)/_component/Title'
import FlexContainer, {
  AlignItems,
  FlexDirection,
  JustifyContent,
} from '@/app/(app)/_cssComp/FlexContainer'
import GridContainer, { ColSpan, GridItem } from '@/app/(app)/_cssComp/GridContainer'
import { ImageMetadata } from '@/app/(app)/_types/ImageMetadata'
import { cn } from '@/lib/utils'
import { ActionType } from '@/utilities/local/enums'
import {
  calculateAspectRatio,
  downloadBase64Image,
  getMimeTypeFromSrc,
} from '@/utilities/local/image'
import ImageLightBox from '../ImageLightbox'

interface Props {
  image: ImageMetadata
  imageActionChange: (action: string, image?: ImageMetadata | null | undefined) => void
  imageSelectionChange: (id: string) => void
  isSelected: boolean
  classname?: string
}

function ImageMetaInfo(props: Props) {
  const { image, imageActionChange, imageSelectionChange, isSelected, classname } = props
  const [openDialog, setOpenDialog] = useState(false)
  const [openImageLightbox, setOpenImageLightbox] = useState(false)

  const downloadImage = async () => {
    //const mimeType = getMimeTypeFromBase64(image.src).split('/')[1]
    const mimeTypeRes = await getMimeTypeFromSrc(image.src)
    const mimeType = mimeTypeRes.split('/')[1]
    downloadBase64Image(image.src, `${image.id}.${mimeType}`)
  }

  const handleSelection = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
    e.stopPropagation()
    imageSelectionChange(image.id)
  }

  return (
    <>
      <Dialog open={openDialog} onOpenChange={setOpenDialog}>
        {openImageLightbox && (
          <ImageLightBox
            image={image}
            onClose={() => setOpenImageLightbox(false)}
            className={`${openImageLightbox ? 'pointer-events-auto' : 'pointer-events-none'}`}
          />
        )}
        <DialogTrigger asChild>
          <div
            className={cn(
              `group w-full overflow-hidden rounded-base border-2 border-border dark:border-dark-border bg-main font-base shadow-light dark:shadow-dark`,
              `${isSelected ? 'border-accent2 border-4' : 'border-border'}`,
              classname,
            )}
          >
            <div className="relative aspect-square w-full">
              <Checkbox
                checked={isSelected}
                onClick={handleSelection}
                className="absolute top-2 left-2 z-10 border-black h-5 w-5 text-primary bg-gray-50 rounded focus:ring-2 focus"
              />
              <ImageWithSkeleton src={image.src} fill alt={''} className="object-cover" />
              <div className="absolute inset-0 flex flex-col justify-end p-3 text-white bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <Text className="truncate text-white">
                  {image.prompt == null ? image.actionType : image.prompt}
                </Text>
                <Text size="sm" variant="caption">
                  {image.width} x {image.height}
                </Text>
              </div>
            </div>
          </div>
        </DialogTrigger>
        <DialogContent
          className={`font-[Outfit] w-[90%] lg:w-[80%] xl:w-[70%] 2xl:w-[60%]`}
          onInteractOutside={(e) => {
            if (openImageLightbox) {
              e.preventDefault()
            }
          }}
        >
          <DialogHeader>
            <DialogTitle>Review Image</DialogTitle>
            <DialogDescription>Take a look at how it turned out!~</DialogDescription>
          </DialogHeader>
          <GridContainer columns={12} className="w-full gap-5">
            <GridItem colSpan={ColSpan.SPAN_6}>
              <div className="relative aspect-square w-full">
                <ImageWithSkeleton
                  src={image.src}
                  fill
                  alt={''}
                  className="object-contain"
                  onClick={() => setOpenImageLightbox(true)}
                />
              </div>
            </GridItem>
            <GridItem colSpan={ColSpan.SPAN_6}>
              <FlexContainer direction={FlexDirection.COL} className="mb-4 gap-2">
                <FlexContainer
                  direction={FlexDirection.ROW}
                  className="gap-2"
                  justify={JustifyContent.START}
                  align={AlignItems.CENTER}
                >
                  <Title level={HeaderLevel.H4}>Image Generation</Title>
                  <Badge>{image.actionType}</Badge>
                </FlexContainer>
                {image.prompt && (
                  <>
                    <Label>Prompt:</Label>
                    <p>{image.prompt}</p>
                  </>
                )}
              </FlexContainer>
              <Divider />
              <GridContainer className="gap-2">
                <GridItem colSpan={ColSpan.SPAN_12}>
                  <Title level={HeaderLevel.H4}>Image Metadata</Title>
                </GridItem>
                <GridItem colSpan={ColSpan.SPAN_6}>
                  <FlexContainer direction={FlexDirection.COL} className="gap-2">
                    <Label>Size:</Label>
                    <Text variant="description" size="sm">
                      {image.width} x {image.height}
                    </Text>
                  </FlexContainer>
                </GridItem>
                <GridItem colSpan={ColSpan.SPAN_6}>
                  <FlexContainer direction={FlexDirection.COL} className="gap-2">
                    <Label>Aspect Ratio:</Label>
                    <Text variant="description" size="sm">
                      {calculateAspectRatio(image.width, image.height)}
                    </Text>
                  </FlexContainer>
                </GridItem>
              </GridContainer>
              <Divider />
              <Title level={HeaderLevel.H4} className="mt-2 mb-2">
                Image Actions
              </Title>
              <FlexContainer direction={FlexDirection.ROW} className="gap-2" wrap={true}>
                <Button onClick={downloadImage} aria-label="Download Image">
                  <Download className="mr-2" /> Download Image
                </Button>
                {/* <Button>Online Coloring</Button> */}
              </FlexContainer>
              <Divider />
              <Title level={HeaderLevel.H4} className="mt-2 mb-2">
                Edit Image
              </Title>
              <FlexContainer direction={FlexDirection.ROW} className="gap-2" wrap={true}>
                {/* <Button
                  onClick={() => {
                    imageActionChange(ActionType.DRAWING_TO_SKETCH, image)
                    setOpenDialog(false)
                  }}
                >
                  Convert to Sketch
                </Button> */}
                <Button
                  onClick={() => {
                    imageActionChange(ActionType.AI_BACKGROUND_REMOVER, image)
                    setOpenDialog(false)
                  }}
                >
                  <Layers2 className="mr-2" /> Remove Background
                </Button>
                <Button
                  onClick={() => {
                    imageActionChange(ActionType.AI_UPSCALER, image)
                    setOpenDialog(false)
                  }}
                >
                  <ImageUpscale />
                  Upscale Image
                </Button>
                <Button
                  onClick={() => {
                    imageActionChange(ActionType.CONTRAST, image)
                    setOpenDialog(false)
                  }}
                >
                  <Contrast />
                  Improve Contrast
                </Button>
              </FlexContainer>
            </GridItem>
          </GridContainer>
          <DialogFooter>
            {/* <FlexContainer direction={FlexDirection.ROW} className="gap-2">
            <Button>???</Button>
          </FlexContainer> */}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}

export default ImageMetaInfo

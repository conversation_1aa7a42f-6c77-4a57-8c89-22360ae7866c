import { NextRequest } from 'next/server'
import { getPayload } from 'payload'
import Use<PERSON><PERSON><PERSON> from '@/app/(app)/_backend/common/dao/UserDAO'
import { errorHandler } from '@/app/(app)/_backend/common/exception/errorHandler'
import UserService from '@/app/(app)/_backend/common/service/UserService'
import {
  generateNetworkFingerprint,
  getNetworkFingerprint,
} from '@/utilities/local/network/clientIp'
import config from '@payload-config'

export interface RegisterRequest {
  firstName: string
  lastName: string
  email: string
  password: string
  fingerprintHash?: string
}

export interface RegisterResponse {
  email: string
}

export async function POST(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const clientIp = getNetworkFingerprint(request)
    const networkFingerprintHash = generateNetworkFingerprint(clientIp)
    const { firstName, lastName, email, password, fingerprintHash } =
      (await request.json()) as RegisterRequest
    const userDAO = new UserDAO(payload)
    const userService = new UserService(userDAO)
    const user = await userService.register(
      email,
      firstName,
      lastName,
      false,
      password,
      networkFingerprintHash,
      fingerprintHash,
    )

    return new Response(
      JSON.stringify({ message: 'Redirect to verify', data: { email: user.email } }),
    )
  } catch (error) {
    return errorHandler(error)
  }
}
